# Unified Transaction System

This directory contains the unified transaction system that consolidates all transaction-related functionality across the OneKitty application.

## Overview

The unified transaction system provides a consistent interface for displaying and managing transactions across different contexts:
- **User Transactions**: Personal transaction history
- **Kitty Transactions**: Kitty-specific contributions and activities
- **Chama Transactions**: Chama group financial activities
- **Event Transactions**: Event-related payments and tickets

## Architecture

### Core Components

1. **TransactionPage** (`transaction_page.dart`)
   - Main unified screen for all transaction types
   - Handles different configurations through `TransactionPageConfig`
   - Provides consistent UI/UX across all transaction types

2. **TransactionController** (`controllers/transaction_controller.dart`)
   - Unified state management for all transaction types
   - Handles loading, filtering, searching, and pagination
   - Integrates with different data sources based on transaction type

3. **TransactionService** (`services/transaction_service.dart`)
   - Unified API service for all transaction operations
   - Handles different endpoints and data parsing
   - Provides consistent data transformation

4. **TransactionType** (`models/transaction_type.dart`)
   - Enum defining different transaction types
   - Configuration for each transaction type
   - Type-specific properties and behaviors

### Widgets

- **TransactionSearchWidget**: Unified search functionality
- **TransactionFilterWidget**: Advanced filtering options
- **UnifiedTransactionItem**: Consistent transaction display

### Utilities

- **TransactionNavigation**: Helper functions for navigation
- Extension methods for easy navigation

## Usage

### Basic Navigation

```dart
import 'package:onekitty/screens/dashboard/pages/transactions/utils/transaction_navigation.dart';

// Navigate to user transactions
Get.toUserTransactions();

// Navigate to kitty transactions
Get.toKittyTransactions(kittyId: 123);

// Navigate to chama transactions
Get.toChamaTransactions(chamaId: 456, accountNo: 'ACC001');

// Navigate to event transactions
Get.toEventTransactions(eventId: 789);
```

### Custom Configuration

```dart
TransactionNavigation.toTransactionsWithConfig(
  TransactionPageConfig(
    transactionType: TransactionType.kitty,
    entityId: 123,
    title: 'Custom Kitty View',
    showExportOptions: false,
    showEditOptions: true,
  ),
);
```

### Direct Usage

```dart
import 'package:onekitty/screens/dashboard/pages/transactions/transaction_page.dart';
import 'package:onekitty/screens/dashboard/pages/transactions/models/transaction_type.dart';

// Create and navigate to transaction page
Get.to(() => TransactionPage(
  config: TransactionPageConfig(
    transactionType: TransactionType.user,
    title: 'My Transactions',
    showExportOptions: true,
  ),
));
```

## Features

### Unified Features
- **Search**: Search across all transaction fields
- **Filtering**: Date range and type-specific filters
- **Pagination**: Efficient loading of large transaction lists
- **Export**: PDF and Excel export capabilities
- **Refresh**: Pull-to-refresh functionality

### Type-Specific Features
- **Kitty Transactions**: Edit transaction details (admin only)
- **Chama Transactions**: Account-based filtering
- **Event Transactions**: Ticket-related information
- **User Transactions**: Cross-kitty transaction view

## Migration Guide

### From Old Transaction Screens

Replace old navigation calls:

```dart
// Old way
Get.to(() => UserTransactionsScreen());
Get.to(() => AllTransactionsScreen(kittyId: 123));
Get.to(() => ChamaTransactionsPage(isFullPage: true));

// New way
Get.toUserTransactions();
Get.toKittyTransactions(kittyId: 123);
Get.toChamaTransactions(chamaId: 456);
```

### Controller Integration

The new system integrates with existing controllers:
- `UserKittyController` for user transactions
- `KittyController` for kitty transactions
- `ChamaController` for chama transactions
- Event controllers for event transactions

## File Structure

```
transactions/
├── transaction_page.dart              # Main unified screen
├── controllers/
│   └── transaction_controller.dart    # Unified controller
├── services/
│   └── transaction_service.dart       # Unified API service
├── models/
│   └── transaction_type.dart          # Transaction type definitions
├── widgets/
│   ├── transaction_search_widget.dart
│   ├── transaction_filter_widget.dart
│   └── unified_transaction_item.dart
├── utils/
│   └── transaction_navigation.dart    # Navigation helpers
└── README.md                          # This file
```

## Benefits

1. **Consistency**: Unified UI/UX across all transaction types
2. **Maintainability**: Single codebase for all transaction functionality
3. **Flexibility**: Easy to add new transaction types or features
4. **Performance**: Optimized loading and caching
5. **Reusability**: Shared components and logic

## Future Enhancements

- Real-time transaction updates
- Advanced analytics and insights
- Bulk operations
- Transaction categories and tags
- Enhanced export formats
- Offline support
