// Integration Example
// Shows how to integrate the new unified transaction system with existing code

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../transactions_exports.dart';

/// Example of how to replace existing transaction navigation in your app
class IntegrationExample {
  
  /// Replace old navigation calls in home screen
  void replaceHomeScreenNavigation() {
    // OLD: Navigate to user transactions
    // Get.to(() => UserTransactionsScreen());
    
    // NEW: Navigate to user transactions
    Get.toUserTransactions();
  }

  /// Replace old navigation calls in kitty details
  void replaceKittyDetailsNavigation(int kittyId, String kittyTitle) {
    // OLD: Navigate to kitty transactions
    // Get.to(() => AllTransactionsScreen(kittyId: kittyId));
    
    // NEW: Navigate to kitty transactions
    Get.toKittyTransactions(
      kittyId: kittyId,
      title: '$kittyTitle Transactions',
    );
  }

  /// Replace old navigation calls in chama details
  void replaceChamaDetailsNavigation(int chamaId, String? accountNo) {
    // OLD: Navigate to chama transactions
    // Get.to(() => ChamaTransactionsPage(
    //   isFullPage: true,
    //   isFromMemberTransactions: false,
    //   accountNo: accountNo,
    // ));
    
    // NEW: Navigate to chama transactions
    Get.toChamaTransactions(
      chamaId: chamaId,
      accountNo: accountNo,
      title: 'Chama Transactions',
    );
  }

  /// Replace old navigation calls in event details
  void replaceEventDetailsNavigation(int eventId, String eventTitle) {
    // OLD: Navigate to event transactions (if existed)
    // Get.to(() => EventTransactionsScreen(eventId: eventId));
    
    // NEW: Navigate to event transactions
    Get.toEventTransactions(
      eventId: eventId,
      title: '$eventTitle Transactions',
    );
  }
}

/// Example widget showing integration in a dashboard
class DashboardTransactionIntegration extends StatelessWidget {
  const DashboardTransactionIntegration({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Quick access to user transactions
        ListTile(
          leading: const Icon(Icons.account_balance_wallet),
          title: const Text('My Transactions'),
          subtitle: const Text('View all your transactions'),
          trailing: const Icon(Icons.arrow_forward_ios),
          onTap: () => Get.toUserTransactions(),
        ),
        
        // Quick access to recent kitty transactions
        ListTile(
          leading: const Icon(Icons.savings),
          title: const Text('Recent Kitty Activity'),
          subtitle: const Text('Latest contributions and activities'),
          trailing: const Icon(Icons.arrow_forward_ios),
          onTap: () => _showKittyTransactionOptions(),
        ),
        
        // Quick access to chama transactions
        ListTile(
          leading: const Icon(Icons.group),
          title: const Text('Chama Transactions'),
          subtitle: const Text('Group financial activities'),
          trailing: const Icon(Icons.arrow_forward_ios),
          onTap: () => _showChamaTransactionOptions(),
        ),
      ],
    );
  }

  void _showKittyTransactionOptions() {
    // Show bottom sheet with kitty options
    Get.bottomSheet(
      Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              title: const Text('All Kitty Transactions'),
              onTap: () {
                Get.back();
                // Navigate to all kitty transactions for a specific kitty
                Get.toKittyTransactions(kittyId: 123);
              },
            ),
            ListTile(
              title: const Text('Admin View'),
              onTap: () {
                Get.back();
                // Navigate with admin privileges
                TransactionNavigation.toTransactionsWithConfig(
                  const TransactionPageConfig(
                    transactionType: TransactionType.kitty,
                    entityId: 123,
                    title: 'Admin Dashboard',
                    showEditOptions: true,
                  ),
                );
              },
            ),
          ],
        ),
      ),
      backgroundColor: Colors.white,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
    );
  }

  void _showChamaTransactionOptions() {
    // Show bottom sheet with chama options
    Get.bottomSheet(
      Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              title: const Text('All Chama Transactions'),
              onTap: () {
                Get.back();
                Get.toChamaTransactions(chamaId: 456);
              },
            ),
            ListTile(
              title: const Text('My Account Transactions'),
              onTap: () {
                Get.back();
                Get.toChamaTransactions(
                  chamaId: 456,
                  accountNo: 'ACC001',
                  title: 'My Account Activity',
                );
              },
            ),
          ],
        ),
      ),
      backgroundColor: Colors.white,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
    );
  }
}

/// Example of how to use the unified system in existing controllers
class ControllerIntegrationExample extends GetxController {
  
  /// Method to navigate to transactions from existing business logic
  void navigateToUserTransactions() {
    // Simple navigation
    Get.toUserTransactions();
  }

  void navigateToKittyTransactions(int kittyId, String kittyTitle) {
    // Navigation with context
    Get.toKittyTransactions(
      kittyId: kittyId,
      title: '$kittyTitle - Transactions',
      showEditOptions: _isUserAdmin(),
    );
  }

  void navigateToTransactionReport(int entityId, TransactionType type) {
    // Navigation for reporting
    TransactionNavigation.toTransactionsWithConfig(
      TransactionPageConfig(
        transactionType: type,
        entityId: entityId,
        title: 'Financial Report',
        showExportOptions: true,
        showEditOptions: false,
      ),
    );
  }

  bool _isUserAdmin() {
    // Your admin check logic here
    return true;
  }
}

/// Example of how to customize the transaction page for specific needs
class CustomTransactionExample {
  
  /// Create a read-only transaction view for reports
  void showTransactionReport(TransactionType type, int entityId) {
    TransactionNavigation.toTransactionsWithConfig(
      TransactionPageConfig(
        transactionType: type,
        entityId: entityId,
        title: 'Financial Report',
        showExportOptions: true,
        showEditOptions: false,
        isFullPage: true,
      ),
    );
  }

  /// Create an admin view with full permissions
  void showAdminTransactionView(int kittyId) {
    TransactionNavigation.toTransactionsWithConfig(
      TransactionPageConfig(
        transactionType: TransactionType.kitty,
        entityId: kittyId,
        title: 'Admin Dashboard - Kitty Management',
        showExportOptions: true,
        showEditOptions: true,
        isFullPage: true,
      ),
    );
  }

  /// Create a minimal view for embedded use
  void showEmbeddedTransactionView(TransactionType type) {
    TransactionNavigation.toTransactionsWithConfig(
      TransactionPageConfig(
        transactionType: type,
        title: 'Recent Activity',
        showExportOptions: false,
        showEditOptions: false,
        isFullPage: false,
      ),
    );
  }
}

/// Benefits of this integration approach:
/// 
/// 1. **Minimal Code Changes**: Just replace navigation calls
/// 2. **Backward Compatibility**: Old screens can coexist during migration
/// 3. **Gradual Migration**: Migrate one screen at a time
/// 4. **Enhanced Features**: Get new features automatically
/// 5. **Consistent UX**: Users get consistent experience across all transaction types
