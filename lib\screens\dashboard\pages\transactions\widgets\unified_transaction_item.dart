// Unified Transaction Item Widget
// Displays transaction items for all transaction types with consistent UI

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';
import 'package:get/get.dart';
import 'package:share_plus/share_plus.dart';
import 'package:onekitty/models/transaction_model.dart';
import 'package:onekitty/models/transaction_edit_models.dart';
import 'package:onekitty/widgets/transaction_edit_dialog.dart' as edit_dialog;
import 'package:onekitty/controllers/user_ktty_controller.dart';

import 'package:onekitty/helpers/colors.dart';
import '../models/transaction_type.dart';

class UnifiedTransactionItem extends StatefulWidget {
  final TransactionModel transaction;
  final TransactionType transactionType;
  final VoidCallback? onTap;
  final bool showEditOption;

  const UnifiedTransactionItem({
    super.key,
    required this.transaction,
    required this.transactionType,
    this.onTap,
    this.showEditOption = false,
  });

  @override
  State<UnifiedTransactionItem> createState() => _UnifiedTransactionItemState();
}

class _UnifiedTransactionItemState extends State<UnifiedTransactionItem>
    with SingleTickerProviderStateMixin {
  bool _isExpanded = false;
  late AnimationController _animationController;
  late Animation<double> _expandAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _expandAnimation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _toggleExpanded() {
    setState(() {
      _isExpanded = !_isExpanded;
      if (_isExpanded) {
        _animationController.forward();
      } else {
        _animationController.reverse();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.w, vertical: 4.h),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: AppColors.background),
        boxShadow: [
          BoxShadow(
            color: AppColors.dark.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: InkWell(
        onTap: widget.onTap,
        borderRadius: BorderRadius.circular(12.r),
        child: Padding(
          padding: EdgeInsets.all(16.w),
          child: Column(
            children: [
              _buildTransactionHeader(),
              SizedBox(height: 12.h),
              _buildTransactionDetails(),
              // Always show action buttons (share is always available)
              SizedBox(height: 12.h),
              _buildActionButtons(context),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTransactionHeader() {
    return Row(
      children: [
        _buildTransactionIcon(),
        SizedBox(width: 12.w),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                _getTransactionTitle(),
                style: const TextStyle(
                  color: Colors.black87,
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
              SizedBox(height: 2.h),
              Text(
                _getTransactionSubtitle(),
                style: const TextStyle(
                  color: Colors.black54,
                  fontSize: 12,
                  fontWeight: FontWeight.w400,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
        Column(
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Text(
              'KES ${_formatAmount(widget.transaction.amount)}',
              style: TextStyle(
                color: _getAmountColor(),
                fontSize: 14,
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: 2.h),
            _buildStatusChip(),
          ],
        ),
      ],
    );
  }

  Widget _buildTransactionIcon() {
    IconData iconData;
    Color iconColor;

    switch (widget.transactionType) {
      case TransactionType.user:
        iconData = Icons.person_outline;
        iconColor = AppColors.primary;
        break;
      case TransactionType.kitty:
        iconData = Icons.savings_outlined;
        iconColor = AppColors.midBlue;
        break;
      case TransactionType.chama:
        iconData = Icons.group_outlined;
        iconColor = AppColors.stackBlue;
        break;
      case TransactionType.event:
        iconData = Icons.event_outlined;
        iconColor = AppColors.mainPurple;
        break;
    }

    return Container(
      width: 40.w,
      height: 40.w,
      decoration: BoxDecoration(
        color: iconColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: Icon(
        iconData,
        color: iconColor,
        size: 20.w,
      ),
    );
  }

  Widget _buildTransactionDetails() {
    return Column(
      children: [
       

        // Always show date
        _buildDetailRow(
          'Date',
          DateFormat('dd MMM yyyy, hh:mm a').format(widget.transaction.createdAt ?? DateTime.now()),
          Icons.access_time,
        ),

        // Always show transaction code if available
        if (widget.transaction.transactionCode != null && widget.transaction.transactionCode!.isNotEmpty) ...[
          SizedBox(height: 8.h),
          _buildDetailRow(
            'Transaction Code',
            widget.transaction.transactionCode!,
            Icons.qr_code_outlined,
          ),
        ],
   // Expandable details
        SizeTransition(
          sizeFactor: _expandAnimation,
          child: Column(
            children: [
              // Transaction reference (if available)
              if (widget.transaction.transactionRef != null && widget.transaction.transactionRef!.isNotEmpty) ...[
                SizedBox(height: 8.h),
                _buildDetailRow(
                  'Reference',
                  widget.transaction.transactionRef!,
                  Icons.receipt_outlined,
                ),
              ],

              // Phone number (if available)
              if (widget.transaction.phoneNumber != null && widget.transaction.phoneNumber!.isNotEmpty) ...[
                SizedBox(height: 8.h),
                _buildDetailRow(
                  'Phone',
                  widget.transaction.phoneNumber!,
                  Icons.phone_outlined,
                ),
              ],

              // Email (if available)
              if (widget.transaction.email != null && widget.transaction.email!.isNotEmpty) ...[
                SizedBox(height: 8.h),
                _buildDetailRow(
                  'Email',
                  widget.transaction.email!,
                  Icons.email_outlined,
                ),
              ],

              // Account number (if available)
              if (widget.transaction.accounNumber != null && widget.transaction.accounNumber!.isNotEmpty) ...[
                SizedBox(height: 8.h),
                _buildDetailRow(
                  'Account',
                  widget.transaction.accounNumber!,
                  Icons.account_balance_outlined,
                ),
              ],

              // Product type (if available)
              if (widget.transaction.product != null && widget.transaction.product!.isNotEmpty) ...[
                SizedBox(height: 8.h),
                _buildDetailRow(
                  'Type',
                  widget.transaction.product!.toUpperCase(),
                  Icons.category_outlined,
                ),
              ],

              // Kitty info (if available and relevant)
              if (_shouldShowKittyInfo()) ...[
                SizedBox(height: 8.h),
                _buildDetailRow(
                  'Kitty',
                  widget.transaction.kittyTitle ?? 'N/A',
                  Icons.savings_outlined,
                ),
              ],

              // Payment reference (if available and different from transaction ref)
              if (widget.transaction.payment_ref != null &&
                  widget.transaction.payment_ref!.isNotEmpty &&
                  widget.transaction.payment_ref != widget.transaction.transactionRef) ...[
                SizedBox(height: 8.h),
                _buildDetailRow(
                  'Payment Ref',
                  widget.transaction.payment_ref!,
                  Icons.payment,
                ),
              ],
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildDetailRow(String label, String value, IconData icon) {
    return Row(
      children: [
        Icon(
          icon,
          size: 16.w,
          color: AppColors.neutralGrey,
        ),
        SizedBox(width: 8.w),
        Text(
          '$label: ',
          style: const TextStyle(
            color: Colors.black54,
            fontSize: 12,
            fontWeight: FontWeight.w400,
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: const TextStyle(
              color: Colors.black87,
              fontSize: 12,
              fontWeight: FontWeight.w400,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }

  Widget _buildStatusChip() {
    final status = widget.transaction.status ?? 'Unknown';
    final isSuccess = status.toLowerCase().contains('success') || 
                     status.toLowerCase().contains('completed');
    final isPending = status.toLowerCase().contains('pending');

    Color backgroundColor;
    Color textColor;

    if (isSuccess) {
      backgroundColor = Colors.green.withOpacity(0.1);
      textColor = Colors.green.shade700;
    } else if (isPending) {
      backgroundColor = Colors.orange.withOpacity(0.1);
      textColor = Colors.orange.shade700;
    } else {
      backgroundColor = Colors.red.withOpacity(0.1);
      textColor = Colors.red.shade700;
    }

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 2.h),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Text(
        status,
        style: TextStyle(
          color: textColor,
          fontSize: 10.sp,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Widget _buildActionButtons(BuildContext context) {
    final bool canEdit = _canEditTransaction();

    return Row(
      children: [
        
        // Dropdown toggle button
        if (_hasAdditionalDetails()) ...[
          
          GestureDetector(
            onTap: _toggleExpanded,
            child: Container(
              margin: const EdgeInsets.all(8),
              padding: const EdgeInsets.all(8),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  AnimatedRotation(
                    turns: _isExpanded ? 0.5 : 0,
                    duration: const Duration(milliseconds: 300),
                    child: Icon(
                      Icons.keyboard_arrow_down,
                      color: AppColors.neutralGrey,
                      size: 20.w,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],

     
        // Edit button - only show if transaction can be edited
        if (canEdit) ...[
          Expanded(
            child: OutlinedButton.icon(
              onPressed: () => _showEditDialog(context),
              icon: Icon(Icons.edit_outlined, size: 16.w),
              label: const Text('Edit'),
              style: OutlinedButton.styleFrom(
                foregroundColor: AppColors.primary,
                side: const BorderSide(color: AppColors.midBlue),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8.r),
                ),
              ),
            ),
          ),
          SizedBox(width: 12.w),
        ],

        // Share button - always available
        Expanded(
          child: ElevatedButton.icon(
            onPressed: () => _shareTransaction(),
            icon: Icon(Icons.share_outlined, size: 16.w),
            label: const Text('Share'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8.r),
              ),
            ),
          ),
        ),
      ],
    );
  }

  String _getTransactionTitle() {
    if (widget.transaction.firstName != null || widget.transaction.secondName != null) {
      return '${widget.transaction.firstName ?? ''} ${widget.transaction.secondName ?? ''}'.trim();
    }
    
    switch (widget.transactionType) {
      case TransactionType.user:
        return 'My Transaction';
      case TransactionType.kitty:
        return widget.transaction.kittyTitle ?? 'Kitty Transaction';
      case TransactionType.chama:
        return 'Chama Transaction';
      case TransactionType.event:
        return 'Event Transaction';
    }
  }

  String _getTransactionSubtitle() {
    return widget.transaction.transactionType ?? widget.transactionType.displayName;
  }

  String _formatAmount(num? amount) {
    if (amount == null) return '0.00';
    return NumberFormat('#,##0.00').format(amount);
  }

  Color _getAmountColor() {
    final type = widget.transaction.typeInOut?.toLowerCase();
    if (type == 'in') return Colors.green;
    if (type == 'out') return Colors.red;
    return AppColors.dark;
  }

  bool _shouldShowKittyInfo() {
    return widget.transactionType == TransactionType.user && 
           widget.transaction.kittyTitle != null && 
           widget.transaction.kittyTitle!.isNotEmpty;
  }

  /// Check if there are additional details to show in the expandable section
  bool _hasAdditionalDetails() {
    return (widget.transaction.transactionRef != null && widget.transaction.transactionRef!.isNotEmpty) ||
           (widget.transaction.phoneNumber != null && widget.transaction.phoneNumber!.isNotEmpty) ||
           (widget.transaction.email != null && widget.transaction.email!.isNotEmpty) ||
           (widget.transaction.accounNumber != null && widget.transaction.accounNumber!.isNotEmpty) ||
           (widget.transaction.product != null && widget.transaction.product!.isNotEmpty) ||
           _shouldShowKittyInfo() ||
           (widget.transaction.payment_ref != null && 
            widget.transaction.payment_ref!.isNotEmpty && 
            widget.transaction.payment_ref != widget.transaction.transactionRef);
  }

  /// Check if transaction can be edited
  /// Only contributions (product == 'contribution' && typeInOut == 'in') can be edited
  bool _canEditTransaction() {
    return widget.transaction.product?.toLowerCase() == 'contributions' &&
           widget.transaction.typeInOut?.toLowerCase() == 'in' ;  
  }

  /// Show transaction edit dialog
  void _showEditDialog(BuildContext context) { 
    edit_dialog.showTransactionEditDialog(
      context: context,
      transaction: widget.transaction,
            
      isAdmin: false, // Default to false for user transactions
      onSave: (formData) async {
        try {
          // Create the edit request
          final editRequest = TransactionEditRequest(
            internalId: widget.transaction.internalId ?? '',
            newFirstName: formData.firstName,
            newSecondName: formData.secondName,
            newPaymentRef: formData.paymentRef,
            showNames: formData.showNames,
            reason: 'User requested edit',
          );

          // Get the controller and call edit transaction
          final controller = Get.find<UserKittyController>();
          final response = await controller.editTransaction(editRequest);

          if (response.success) {
            Navigator.of(context).pop(); // Close the edit dialog
            Get.snackbar(
              'Success',
              'Transaction updated successfully',
              backgroundColor: Colors.green,
              colorText: Colors.white,
            );
          } else {
            Get.snackbar(
              'Error',
              response.message,
              backgroundColor: Colors.red,
              colorText: Colors.white,
            );
          }
        } catch (e) {
          Get.snackbar(
            'Error',
            'Failed to update transaction: $e',
            backgroundColor: Colors.red,
            colorText: Colors.white,
          );
        }
      },
    );
  }

  /// Share transaction details
  void _shareTransaction() async {
    final format = DateFormat('dd MMM yyyy, HH:mm');
    final createdAt = widget.transaction.createdAt ?? DateTime.now();

    String shareMsg = _buildShareMessage(format, createdAt);

    try {
      await Share.share(shareMsg, subject: 'Transaction Details');
    } catch (e) {
      Get.snackbar(
        'Error',
        'Failed to share transaction: $e',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  /// Build share message with all available transaction details
  String _buildShareMessage(DateFormat format, DateTime createdAt) {
    List<String> messageParts = [];

    // Transaction title
    messageParts.add('=== TRANSACTION DETAILS ===');

    // Name (if available)
    if (widget.transaction.firstName != null || widget.transaction.secondName != null) {
      String fullName = '${widget.transaction.firstName ?? ''} ${widget.transaction.secondName ?? ''}'.trim();
      if (fullName.isNotEmpty) {
        messageParts.add('Name: $fullName');
      }
    }

    // Phone number (if available)
    if (widget.transaction.phoneNumber != null && widget.transaction.phoneNumber!.isNotEmpty) {
      messageParts.add('Phone: ${widget.transaction.phoneNumber}');
    }

    // Email (if available)
    if (widget.transaction.email != null && widget.transaction.email!.isNotEmpty) {
      messageParts.add('Email: ${widget.transaction.email}');
    }

    // Amount
    messageParts.add('Amount: KES ${_formatAmount(widget.transaction.amount)}');

    // Transaction code (if available)
    if (widget.transaction.transactionCode != null && widget.transaction.transactionCode!.isNotEmpty) {
      messageParts.add('Transaction Code: ${widget.transaction.transactionCode}');
    }

    // Transaction reference (if available)
    if (widget.transaction.transactionRef != null && widget.transaction.transactionRef!.isNotEmpty) {
      messageParts.add('Reference: ${widget.transaction.transactionRef}');
    }

    // Status (if available)
    if (widget.transaction.status != null && widget.transaction.status!.isNotEmpty) {
      messageParts.add('Status: ${widget.transaction.status}');
    }

    // Date
    messageParts.add('Date: ${format.format(createdAt.toLocal())}');

    // Kitty info (if available)
    if (widget.transaction.kittyId != null) {
      if (widget.transaction.kittyTitle != null && widget.transaction.kittyTitle!.isNotEmpty) {
        messageParts.add('Kitty: ${widget.transaction.kittyTitle}');
      }
      messageParts.add('Kitty Link: https://onekitty.co.ke/kitty/${widget.transaction.kittyId}');
    }

    // Product type (if available)
    if (widget.transaction.product != null && widget.transaction.product!.isNotEmpty) {
      messageParts.add('Type: ${widget.transaction.product}');
    }

    // Channel code (if available)
    if (widget.transaction.channelCode != null && widget.transaction.channelCode!.isNotEmpty) {
      messageParts.add('Channel: ${widget.transaction.channelCode}');
    }

    return messageParts.join('\n');
  }
}