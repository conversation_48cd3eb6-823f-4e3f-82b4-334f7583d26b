// Unified Transaction System Exports
// Single import file for all transaction-related components

// Core Components
export 'transaction_page.dart';
export 'models/transaction_type.dart';
export 'controllers/transaction_controller.dart';
export 'services/transaction_service.dart';

// Widgets
export 'widgets/simple_transaction_search.dart';
export 'widgets/simple_transaction_filter.dart';
export 'widgets/unified_transaction_item.dart';

// Utilities
export 'utils/transaction_navigation.dart';

// Examples (for development/documentation)
export 'examples/migration_example.dart';

/// Usage:
/// 
/// ```dart
/// import 'package:onekitty/screens/dashboard/pages/transactions/transactions_exports.dart';
/// 
/// // Now you can use any transaction component:
/// Get.toUserTransactions();
/// Get.toKittyTransactions(kittyId: 123);
/// 
/// // Or create custom configurations:
/// TransactionNavigation.toTransactionsWithConfig(
///   TransactionPageConfig(
///     transactionType: TransactionType.kitty,
///     entityId: 123,
///   ),
/// );
/// ```
