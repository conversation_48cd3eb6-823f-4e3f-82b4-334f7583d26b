// Unified Transaction System - Workflow Examples
// Demonstrates how to use the new unified transaction system

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:onekitty/screens/dashboard/pages/transactions/utils/transaction_navigation.dart';
import '../transaction_page.dart';
import '../models/transaction_type.dart'; 

/// Example class showing different ways to navigate to transaction screens
class TransactionWorkflowExamples {
  
  /// Example 1: Basic User Transactions
  static void openUserTransactions() {
    // Using extension method (recommended)
    Get.toUserTransactions();
    
    // Or using direct navigation
    Get.to(() => TransactionPage(
      config: TransactionPageConfig(
        transactionType: TransactionType.user,
        title: 'My Transactions',
      ),
    ));
  }
  
  /// Example 2: Kitty Transactions with specific ID
  static void openKittyTransactions(int kittyId, {String? customTitle}) {
    // Using extension method (recommended)
    Get.toKittyTransactions(
      kittyId: kittyId,
      title: customTitle ?? 'Kitty Transactions',
    );
  }
  
  /// Example 3: Chama Transactions
  static void openChamaTransactions(int chamaId) {
    // Using extension method
    Get.toChamaTransactions(
      chamaId: chamaId,
      title: 'Chama Financial Records',
    );
  }
  
  /// Example 4: Event Transactions
  static void openEventTransactions(int eventId) {
    // Using extension method
    Get.toEventTransactions(
      eventId: eventId,
      title: 'Event Transactions',
    );
  }
  
  /// Example 5: Custom Configuration
  static void openCustomTransactionView() {
    final config = TransactionPageConfig(
      transactionType: TransactionType.user,
      title: 'Custom Transaction View',
      // Add any custom parameters here
    );
    
    Get.to(() => TransactionPage(config: config));
  }
}

/// Widget demonstrating the unified transaction system
class TransactionWorkflowDemo extends StatelessWidget {
  const TransactionWorkflowDemo({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Transaction System Demo'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            const Text(
              'Unified Transaction System',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            const Text(
              'One system handles all transaction types',
              style: TextStyle(
                fontSize: 16,
                color: Colors.black54,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 32),
            
            // User Transactions
            _buildDemoButton(
              'My Transactions',
              'View your personal transactions',
              Colors.blue,
              () => Get.toUserTransactions(),
            ),
            
            const SizedBox(height: 16),
            
            // Kitty Transactions
            _buildDemoButton(
              'Kitty Transactions',
              'View transactions for a specific kitty',
              Colors.green,
              () => Get.toKittyTransactions(kittyId: 123),
            ),
            
            const SizedBox(height: 16),
            
            // Chama Transactions
            _buildDemoButton(
              'Chama Transactions',
              'View chama financial records',
              Colors.orange,
              () => Get.toChamaTransactions(chamaId: 456),
            ),
            
            const SizedBox(height: 16),
            
            // Event Transactions
            _buildDemoButton(
              'Event Transactions',
              'View event-related transactions',
              Colors.purple,
              () => Get.toEventTransactions(eventId: 789),
            ),
            
            const SizedBox(height: 32),
            
            const Text(
              '✅ All transaction types use the same unified interface\n'
              '✅ Consistent user experience across all screens\n'
              '✅ Easy to maintain and extend\n'
              '✅ Type-safe navigation with proper error handling',
              style: TextStyle(
                fontSize: 14,
                color: Colors.black87,
                height: 1.5,
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildDemoButton(
    String title,
    String subtitle,
    Color color,
    VoidCallback onPressed,
  ) {
    return Card(
      elevation: 2,
      child: InkWell(
        onTap: onPressed,
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            children: [
              Container(
                width: 48,
                height: 48,
                decoration: BoxDecoration(
                  color: color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.account_balance_wallet,
                  color: color,
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      subtitle,
                      style: const TextStyle(
                        fontSize: 14,
                        color: Colors.black54,
                      ),
                    ),
                  ],
                ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                color: Colors.black54,
                size: 16,
              ),
            ],
          ),
        ),
      ),
    );
  }
}

/// Migration helper showing old vs new patterns
class MigrationExamples {
  
  /// OLD WAY - Multiple different screens
  static void oldWayExamples() {
    // OLD: Different screens for each type
    // Get.to(() => UserTransactionsScreen());
    // Get.to(() => AllTransactionsScreen(kittyId: 123));
    // Get.to(() => ChamaTransactionsScreen(chamaId: 456));
    // Get.to(() => EventTransactionsScreen(eventId: 789));
  }
  
  /// NEW WAY - Unified system with extension methods
  static void newWayExamples() {
    // NEW: Single system with type-safe navigation
    Get.toUserTransactions();
    Get.toKittyTransactions(kittyId: 123);
    Get.toChamaTransactions(chamaId: 456);
    Get.toEventTransactions(eventId: 789);
  }
  
  /// Benefits of the new system
  static const List<String> benefits = [
    '✅ Single codebase to maintain',
    '✅ Consistent UI/UX across all transaction types',
    '✅ Type-safe navigation with compile-time checks',
    '✅ Unified error handling and loading states',
    '✅ Easy to add new transaction types',
    '✅ Better performance with shared components',
    '✅ Simplified testing and debugging',
  ];
}
